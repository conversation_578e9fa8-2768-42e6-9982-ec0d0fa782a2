<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>INTER NRW - Weniger WhatsApp. Mehr Fußball.</title>
    <meta name="description" content="Wöchentliche Fußball-Anmeldung für INTER NRW" />
    <meta name="author" content="INTER NRW" />

    <meta property="og:title" content="INTER NRW - Weniger WhatsApp. Mehr Fußball." />
    <meta property="og:description" content="Wöchentliche Fußball-Anmeldung für INTER NRW" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/images/logo.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="/images/logo.png" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- PWA -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="icon" href="/favicon-32x32.png" type="image/png" sizes="32x32" />
    <link rel="icon" href="/favicon-16x16.png" type="image/png" sizes="16x16" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#FFFFFF" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#080808" media="(prefers-color-scheme: dark)" />

    <!-- Service Worker update helper -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", function () {
          // When a new service worker takes control, reload the page
          navigator.serviceWorker.addEventListener("controllerchange", () => {
            window.location.reload();
          });
        });
      }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
