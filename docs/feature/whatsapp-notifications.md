# WhatsApp Notifications - Übersicht aller Benachrichtigungen

Diese Dokumentation beschreibt alle Szenarien, in denen WhatsApp-Benachrichtigungen gesendet werden.

## Überblick

WhatsApp-Benachrichtigungen werden automatisch an die Gruppe gesendet, um Spieler über wichtige Ereignisse zu informieren. Die Benachrichtigungen können in den Admin-Einstellungen aktiviert oder deaktiviert werden.

**Neue Features (2024):**

- ✅ Reserve-Nummer bei Anmeldung wird angezeigt
- 🔄 Nachrücken-Benachrichtigungen wenn Reserve-Spieler confirmed werden

## Benachrichtigungsszenarien

### 1. Spieler An- und Abmeldungen

#### Spieler meldet sich an

- **Auslöser:** Ein Spieler ändert seinen Status von "pending" oder "out" zu "in"
- **Nachricht (bestätigt):** ✅ _[Spie<PERSON>name]_ hat sich zum Spiel angemeldet.
- **<PERSON><PERSON><PERSON><PERSON> (Reserve):** ✅ _[Spielername]_ hat sich zum Spiel angemeldet (Reserve #2).
- **Implementierung:** `notifyPlayerSignup(player, reserveNumber?)`
- **Datei:** `src/components/player-list/index.tsx` (in `confirmStatusChange`)
- **Technische Details:** Reserve-Nummer wird automatisch berechnet basierend auf aktueller Spieleranzahl und Team-Einstellungen

#### Spieler meldet sich ab

- **Auslöser:** Ein Spieler ändert seinen Status von "pending" oder "in" zu "out"
- **Nachricht:** ❌ _[Spielername]_ hat sich vom Spiel abgemeldet.
- **Implementierung:** `notifyPlayerSignoff(player)`
- **Datei:** `src/components/player-list/index.tsx` (in `confirmStatusChange`)

#### Reserve-Spieler rückt nach

- **Auslöser:** Ein Reserve-Spieler rückt automatisch zu "confirmed" Status nach, weil ein anderer Spieler sich abgemeldet hat
- **Nachricht:** 🔄 _[Spielername]_ ist nachgerückt und kann jetzt am Spiel teilnehmen!
- **Implementierung:** `notifyPlayerMovedFromReserve(player)`
- **Datei:** `src/components/player-list/index.tsx` (in `confirmStatusChange`, wird nach Abmeldung automatisch geprüft)
- **Technische Details:** Nach jeder Abmeldung wird simuliert, welche Reserve-Spieler in den confirmed Status nachrücken und entsprechende Benachrichtigungen versendet

### 2. Spielanmeldung Status-Änderungen

#### Anmeldung wird geöffnet

- **Auslöser:** Admin öffnet die Anmeldung für ein Spiel (manuell oder automatisch)
- **Nachricht:**

  ```
  🟢 *ANMELDUNG GEÖFFNET*

  Die Anmeldung für das Spiel am *[Datum]* um *[Uhrzeit] Uhr* ist jetzt geöffnet. Bitte meldet euch rechtzeitig an oder ab!
  ```

- **Implementierung:** `notifyGameRegistrationOpened(session)`
- **Datei:** `src/components/admin/GameManagementPage.tsx` (in `handleToggleSignup`)

#### Anmeldung wird geschlossen

- **Auslöser:** Admin schließt die Anmeldung für ein Spiel
- **Nachricht:**

  ```
  🔴 *ANMELDUNG GESCHLOSSEN*

  Die Anmeldung für das Spiel am *[Datum]* um *[Uhrzeit] Uhr* wurde geschlossen. Keine weiteren An- oder Abmeldungen möglich.
  ```

- **Implementierung:** `notifyGameRegistrationClosed(session)`
- **Datei:** `src/components/admin/GameManagementPage.tsx` (in `handleToggleSignup`)

### 3. Team-Generierung

#### Teams werden erstellt und gespeichert

- **Auslöser:** Admin generiert Teams und speichert sie (macht sie offiziell)
- **Nachricht:**

  ```
  ⚽ *TEAMS GENERIERT*

  Die Teams für das Spiel am *[Datum]* um *[Uhrzeit] Uhr* wurden erstellt:

  *Team [Name]*:
  - [Spieler 1]
  - [Spieler 2]
  - [...]

  *Team [Name]*:
  - [Spieler 1]
  - [Spieler 2]
  - [...]
  ```

- **Implementierung:** `notifyTeamsGenerated(session, teams)`
- **Datei:** `src/components/admin/GameManagementPage.tsx` (in `handleApproveTeams`)

### 4. MVP Voting

#### MVP-Abstimmung wird geöffnet

- **Auslöser:** Spiel wird als "gespielt" markiert, automatisch wird MVP-Voting geöffnet
- **Nachricht:**

  ```
  🏆 *MVP VOTING GESTARTET*

  Die MVP-Abstimmung für das Spiel vom *[Datum]* ist jetzt eröffnet!

  Wer war heute der wertvollste Spieler auf dem Platz? Gib jetzt deine Stimme ab und würdige die herausragende Leistung deiner Mitspieler! 🌟

  Deine Stimme zählt - mach mit bei der MVP-Wahl! 🗳️
  ```

- **Implementierung:** `notifyMVPVotingOpened(gameDate)`
- **Datei:** `src/services/mvpService.ts` (beim Erstellen von MVP-Voting-Perioden)

#### MVP-Abstimmung wird geschlossen und Gewinner bekannt gegeben

- **Auslöser:** MVP-Abstimmung läuft ab oder wird manuell geschlossen
- **Nachricht (ein Gewinner):**

  ```
  🎉 *MVP VOTING BEENDET*

  Die Stimmen für das Spiel vom *[Datum]* sind ausgezählt!

  👑 *MVP des Spiels*:
  *[Spielername]*

  Herzlichen Glückwunsch zu dieser herausragenden Leistung! 🌟
  ```

- **Nachricht (mehrere Gewinner):**

  ```
  🎉 *MVP VOTING BEENDET*

  Die Stimmen für das Spiel vom *[Datum]* sind ausgezählt!

  👑 *MVPs des Spiels*:
  *[Spielername 1]*
  *[Spielername 2]*

  Herzlichen Glückwunsch an alle Gewinner für ihre außergewöhnlichen Leistungen! 🌟
  ```

- **Implementierung:** `notifyMVPVotingClosed(gameDate, winners)`
- **Datei:** `src/services/mvpService.ts` (beim Schließen von MVP-Voting-Perioden)

### 5. Spiel-Abbruch

#### Spiel wird abgebrochen

- **Auslöser:** Admin bricht ein geplantes Spiel ab
- **Nachricht:**

  ```
  ❌ *SPIEL ABGEBROCHEN*

  Das Spiel am *[Datum]* um *[Uhrzeit] Uhr* wurde leider abgebrochen.

  Bleibt dran für das nächste Spiel! ⚽
  ```

- **Implementierung:** `notifyGameCancelled(session)`
- **Datei:** `src/components/admin/GameManagementPage.tsx` (in `handleCancelMatch`)

## Technische Details

### Konfiguration

- WhatsApp-Benachrichtigungen können in den Admin-Einstellungen ein-/ausgeschaltet werden
- Die Einstellung wird in der Datenbank unter `app_settings` mit dem Key `whatsapp_notifications_enabled` gespeichert
- Realtime-Updates sorgen dafür, dass Änderungen sofort wirksam werden

### Implementierung

- Alle Notification-Funktionen befinden sich in `src/services/whatsappService.ts`
- Die API verwendet einen externen WhatsApp-Service unter `https://wa.internrw.de/.netlify/functions/internrw-wa-send`
- Im Development-Modus wird ein `testMode: true` Parameter gesendet
- Fehler bei Benachrichtigungen werden geloggt, beeinträchtigen aber nicht die Hauptfunktionalität

### Fehlerbehandlung

- Wenn WhatsApp-Benachrichtigungen deaktiviert sind, werden Nachrichten nur geloggt
- API-Fehler werden abgefangen und geloggt, aber dem Benutzer nicht angezeigt
- Fehlende API-Token werden behandelt und geloggt

### Tests

- Alle Notification-Funktionen haben entsprechende Unit-Tests in `src/services/__tests__/whatsappService.test.ts`
- Tests überprüfen die korrekte Formatierung der Nachrichten und API-Aufrufe
- Inklusive Tests für Reserve-Nummer-Anzeige und Nachrücken-Benachrichtigungen
- Mocks sind für alle externen Abhängigkeiten vorhanden
