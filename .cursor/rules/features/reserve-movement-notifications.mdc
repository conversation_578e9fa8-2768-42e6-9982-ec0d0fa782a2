---
description: 
globs: 
alwaysApply: false
---
# Reserve Number Notifications

## What
WhatsApp-Benachrichtigungen zeigen jetzt die Reserve-Nummer an, wenn sich ein Spieler anmeldet aber auf der Reserveliste steht.

## Why
Spieler sollen sofort wissen, ob sie bestätigt sind oder auf Reserve stehen und welche Nummer sie haben.

## How

### Einfache Implementierung (5 Zeilen Code)

1. **Erweiterte WhatsApp-Funktion**: `notifyPlayerSignup(player, reserveNumber?)` 
2. **Reserve-Berechnung**: Nutzt bestehende `calculatePlayerLists()` Logik
3. **Simulation**: Berechnet Reserve-Status für den sich anmeldenden Spieler

```typescript
// Bei Anmeldung Reserve-Status prüfen und Notification senden
const { reserves } = calculatePlayerLists(updatedPlayers);
const reserveNumber = reserves.find(p => p.id === playerId)?.reserveNumber;
await notifyPlayerSignup(player, reserveNumber);
```

### Nachrichten
- **Bestätigt**: "✅ <PERSON>ermann hat sich zum Spiel angemeldet."
- **Reserve**: "✅ Max Mustermann hat sich zum Spiel angemeldet (Reserve #2)."

## Where
- `src/services/whatsappService.ts` - Erweiterte `notifyPlayerSignup` Funktion  
- `src/components/player-list/index.tsx` - Reserve-Berechnung bei Anmeldung
- `src/services/__tests__/whatsappService.test.ts` - Test für Reserve-Nachrichten
