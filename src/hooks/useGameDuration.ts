import { useEffect, useState, useMemo } from "react";
import { GameSession } from "@/types";
import { useTeamSettings } from "./useTeamSettings";

export function useGameDuration(currentSession: GameSession | null, inPlayerCount: number) {
  const {
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    loading: teamSettingsLoading,
  } = useTeamSettings();
  const [displayDuration, setDisplayDuration] = useState("");

  const calculatedDuration = useMemo(() => {
    if (teamSettingsLoading) return "";

    if (currentSession?.duration_minutes) {
      return `${currentSession.duration_minutes} Minuten`;
    }

    if (inPlayerCount >= 20) {
      return `${duration4Teams} Minuten (4 Teams)`;
    }

    if (inPlayerCount >= 15 && inPlayerCount <= 19 && allow3Teams) {
      return `${duration3Teams} Minuten (3 Teams)`;
    }

    return `${duration2Teams} Minuten (2 Teams)`;
  }, [
    currentSession?.duration_minutes,
    inPlayerCount,
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    teamSettingsLoading,
  ]);

  useEffect(() => {
    setDisplayDuration(calculatedDuration);
  }, [calculatedDuration]);

  return { displayDuration, isLoading: teamSettingsLoading };
}
