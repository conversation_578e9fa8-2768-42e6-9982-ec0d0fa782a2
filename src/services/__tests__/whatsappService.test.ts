import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from "vitest";
import { Player, GameSession, Team } from "@/types";

let whatsappService: typeof import("../whatsappService");

// Mock fetch
global.fetch = vi.fn();

// Mock environment variables for testing
vi.mock("import.meta", () => ({
  env: {
    DEV: true,
    VITE_WHATSAPP_API_TOKEN: "test-token",
  },
}));

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => {
  const mockSupabase = {
    from: vi.fn(() => mockSupabase),
    select: vi.fn(() => mockSupabase),
    eq: vi.fn(() => mockSupabase),
    single: vi.fn(() =>
      Promise.resolve({
        data: { value: "true" },
        error: null,
      })
    ),
    on: vi.fn(() => mockSupabase),
    subscribe: vi.fn(() => ({ unsubscribe: vi.fn() })),
    channel: vi.fn(() => mockSupabase),
  };

  return {
    supabase: mockSupabase,
  };
});

// Mock date-fns format function
vi.mock("date-fns", () => ({
  format: vi.fn((_date, formatStr) => {
    if (formatStr.includes("EEEE")) return "Freitag, 01. Januar 2023";
    if (formatStr.includes("HH:mm")) return "21:00";
    return "01.01.2023";
  }),
}));

describe("WhatsApp Service", () => {
  const mockPlayer: Player = {
    id: "player-123",
    name: "Test Player",
    rating: 75,
    status: "in",
    is_active: true,
  };

  const mockGameSession: GameSession = {
    id: "session-123",
    date: new Date("2023-01-01T21:00:00"),
    signupOpensAt: new Date("2022-12-28T12:00:00"),
    isSignupOpen: true,
    isTeamGenerated: false,
    status: "scheduled",
  };

  const mockTeams: Team[] = [
    {
      id: "team-1",
      name: "Rot",
      averageRating: 70,
      players: [
        { id: "player-1", name: "Player 1", rating: 70, status: "in", is_active: true },
        { id: "player-2", name: "Player 2", rating: 75, status: "in", is_active: true },
        { id: "player-3", name: "Player 3", rating: 65, status: "in", is_active: true },
      ],
    },
    {
      id: "team-2",
      name: "Blau",
      averageRating: 72,
      players: [
        { id: "player-4", name: "Player 4", rating: 72, status: "in", is_active: true },
        { id: "player-5", name: "Player 5", rating: 78, status: "in", is_active: true },
        { id: "player-6", name: "Player 6", rating: 66, status: "in", is_active: true },
      ],
    },
  ];

  const mockResponse = {
    ok: true,
    json: () => Promise.resolve({ success: true }),
  };

  beforeAll(async () => {
    // Stub environment variables *before* the module under test initializes its constants.
    vi.stubEnv("VITE_WHATSAPP_API_TOKEN", "test-token");
    vi.stubEnv("DEV", true); // import.meta.env.DEV is typically a boolean

    vi.resetModules(); // Reset module cache to ensure fresh import with new env vars
    const module = await import("../whatsappService");
    whatsappService = module; // Assign the dynamically imported module

    // Mock the areWhatsAppNotificationsEnabled function to return true by default for all tests in this suite
    // This spy is on the dynamically imported module.
    vi.spyOn(whatsappService, "areWhatsAppNotificationsEnabled").mockResolvedValue(true);
  });

  beforeEach(() => {
    vi.resetAllMocks(); // This resets call counts etc. for spies and mocks
    (fetch as any).mockResolvedValue(mockResponse);
    // The spyOn for areWhatsAppNotificationsEnabled has been moved to beforeAll.
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  afterAll(() => {
    vi.unstubAllEnvs();
    vi.resetModules(); // Clean up module cache
  });

  describe("sendWhatsAppMessage", () => {
    it("should call the WhatsApp API with correct parameters", async () => {
      await whatsappService.sendWhatsAppMessage("Test message");

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: "Test message",
            testMode: true, // In dev environment
          }),
        })
      );
    });

    it("should handle API errors gracefully", async () => {
      (fetch as any).mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve("Internal server error"),
      });

      await expect(whatsappService.sendWhatsAppMessage("Test message")).rejects.toThrow(
        "Failed to send WhatsApp message: 500 Internal server error"
      );
    });
  });

  describe("notifyPlayerSignup", () => {
    it("should format signup message correctly", async () => {
      await whatsappService.notifyPlayerSignup(mockPlayer);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message: "✅ *Test Player* hat sich zum Spiel angemeldet.",
            testMode: true,
          }),
        })
      );
    });

    it("should format reserve signup message correctly", async () => {
      await whatsappService.notifyPlayerSignup(mockPlayer, 3);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message: "✅ *Test Player* hat sich zum Spiel angemeldet (Reserve #3).",
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyPlayerSignoff", () => {
    it("should format signoff message correctly", async () => {
      await whatsappService.notifyPlayerSignoff(mockPlayer);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message: "❌ *Test Player* hat sich vom Spiel abgemeldet.",
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyPlayerMovedFromReserve", () => {
    it("should format move-up message correctly", async () => {
      await whatsappService.notifyPlayerMovedFromReserve(mockPlayer);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message: "🔄 *Test Player* ist nachgerückt und kann jetzt am Spiel teilnehmen!",
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyGameRegistrationOpened", () => {
    it("should format registration opened message correctly", async () => {
      await whatsappService.notifyGameRegistrationOpened(mockGameSession);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message:
              "🟢 *ANMELDUNG GEÖFFNET*\n\nDie Anmeldung für das Spiel am *Freitag, 01. Januar 2023* um *21:00 Uhr* ist jetzt geöffnet. Bitte meldet euch rechtzeitig an oder ab!",
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyGameRegistrationClosed", () => {
    it("should format registration closed message correctly", async () => {
      await whatsappService.notifyGameRegistrationClosed(mockGameSession);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            message:
              "🔴 *ANMELDUNG GESCHLOSSEN*\n\nDie Anmeldung für das Spiel am *Freitag, 01. Januar 2023* um *21:00 Uhr* wurde geschlossen. Keine weiteren An- oder Abmeldungen möglich.",
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyTeamsGenerated", () => {
    it("should format teams generated message correctly", async () => {
      await whatsappService.notifyTeamsGenerated(mockGameSession, mockTeams);

      const expectedMessage = `⚽ *TEAMS GENERIERT*\n\nDie Teams für das Spiel am *Freitag, 01. Januar 2023* um *21:00 Uhr* wurden erstellt:\n\n*Team Rot*:\n- Player 1\n- Player 2\n- Player 3\n\n*Team Blau*:\n- Player 4\n- Player 5\n- Player 6\n`;

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: expectedMessage,
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyMVPVotingOpened", () => {
    it("should format MVP voting opened message correctly", async () => {
      await whatsappService.notifyMVPVotingOpened(new Date("2023-01-01T21:00:00"));

      const expectedMessage = `🏆 *MVP VOTING GESTARTET*\n\nDie MVP-Abstimmung für das Spiel vom *Freitag, 01. Januar 2023* ist jetzt eröffnet!\n\nWer war heute der wertvollste Spieler auf dem Platz? Gib jetzt deine Stimme ab und würdige die herausragende Leistung deiner Mitspieler! 🌟\n\nDeine Stimme zählt - mach mit bei der MVP-Wahl! 🗳️`;

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: expectedMessage,
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyMVPVotingClosed", () => {
    const mockMVPWinners = [{ playerName: "MVP Player", matchId: "session-123" }];

    const mockMultipleMVPWinners = [
      { playerName: "MVP Player 1", matchId: "session-123" },
      { playerName: "MVP Player 2", matchId: "session-123" },
    ];

    it("should format MVP voting closed message correctly with single winner", async () => {
      await whatsappService.notifyMVPVotingClosed(new Date("2023-01-01T21:00:00"), mockMVPWinners);

      const expectedMessage = `🎉 *MVP VOTING BEENDET*\n\nDie Stimmen für das Spiel vom *Freitag, 01. Januar 2023* sind ausgezählt!\n\n👑 *MVP des Spiels*:\n*MVP Player*\n\nHerzlichen Glückwunsch zu dieser herausragenden Leistung! 🌟`;

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: expectedMessage,
            testMode: true,
          }),
        })
      );
    });

    it("should format MVP voting closed message correctly with multiple winners", async () => {
      await whatsappService.notifyMVPVotingClosed(
        new Date("2023-01-01T21:00:00"),
        mockMultipleMVPWinners
      );

      const expectedMessage = `🎉 *MVP VOTING BEENDET*\n\nDie Stimmen für das Spiel vom *Freitag, 01. Januar 2023* sind ausgezählt!\n\n👑 *MVPs des Spiels*:\n*MVP Player 1*\n*MVP Player 2*\n\nHerzlichen Glückwunsch an alle Gewinner für ihre außergewöhnlichen Leistungen! 🌟`;

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: expectedMessage,
            testMode: true,
          }),
        })
      );
    });
  });

  describe("notifyGameCancelled", () => {
    it("should format game cancelled message correctly", async () => {
      await whatsappService.notifyGameCancelled(mockGameSession);

      const expectedMessage = `❌ *SPIEL ABGEBROCHEN*\n\nDas Spiel am *Freitag, 01. Januar 2023* um *21:00 Uhr* wurde leider abgebrochen.\n\nBleibt dran für das nächste Spiel! ⚽`;

      expect(fetch).toHaveBeenCalledWith(
        "https://wa.internrw.de/.netlify/functions/internrw-wa-send",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer test-token",
          }),
          body: JSON.stringify({
            message: expectedMessage,
            testMode: true,
          }),
        })
      );
    });
  });
});
