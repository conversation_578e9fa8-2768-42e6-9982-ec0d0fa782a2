import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle, CardDescription } from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";

/**
 * Component displayed when no game is scheduled
 * Shows a message that no active game is planned
 */
export function NoGameScheduled() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Nächstes Spiel</CardTitle>
        <CardDescription>Informationen zum nächsten geplanten Spiel</CardDescription>
        <Separator />
      </CardHeader>
      <CardContent className="p-6 text-center">
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
            Kein aktives Spiel geplant
          </h3>
          <p className="text-gray-500 dark:text-gray-400 max-w-md">
            Es ist derzeit kein Spiel geplant oder das nächste Spiel wurde noch nicht festgelegt.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
