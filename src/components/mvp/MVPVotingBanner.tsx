import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Trophy, Check, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";

export function MVPVotingBanner() {
  const navigate = useNavigate();
  const [hasVoted, setHasVoted] = useState(false);
  const [votedPlayer, setVotedPlayer] = useState<{ name: string; jerseyNumber?: number } | null>(
    null
  );
  const [gameDate, setGameDate] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBannerHidden, setIsBannerHidden] = useState(false);
  const [votingPeriodId, setVotingPeriodId] = useState<string | null>(null);

  useEffect(() => {
    const checkVotingStatus = async () => {
      try {
        // Get the active voting period
        const { data: votingPeriod } = await (supabase
          .from("mvp_voting_periods" as any)
          .select("id, game_session_id, game_sessions(date)")
          .eq("is_open", true)
          .gte("ends_at", new Date().toISOString())
          .maybeSingle() as any);

        if (!votingPeriod) {
          setIsLoading(false);
          return;
        }

        setVotingPeriodId(votingPeriod.id);

        // Check if banner is dismissed for this voting period
        const dismissedBanners = JSON.parse(localStorage.getItem("dismissedMvpBanners") || "{}");
        if (dismissedBanners[votingPeriod.id]) {
          setIsBannerHidden(true);
        }

        // Format game date if available
        if (votingPeriod.game_sessions && votingPeriod.game_sessions.date) {
          const date = new Date(votingPeriod.game_sessions.date);
          setGameDate(format(date, "dd.MM.yyyy", { locale: de }));
        }

        // Check if device has already voted
        const deviceId = localStorage.getItem("deviceId");
        let userVotedFor: string | null = null;
        let localHasVoted = false;

        if (deviceId) {
          const { data: existingVote } = await (supabase
            .from("mvp_votes" as any)
            .select("player_id")
            .eq("voting_period_id", votingPeriod.id)
            .eq("voter_device_id", deviceId)
            .maybeSingle() as any);

          if (existingVote) {
            localHasVoted = true;
            userVotedFor = existingVote.player_id;
          }
        }

        // Also check if authenticated user has already voted
        if (!localHasVoted) {
          const { data: user } = await supabase.auth.getUser();

          if (user && user.user) {
            const { data: existingUserVote } = await (supabase
              .from("mvp_votes" as any)
              .select("player_id")
              .eq("voting_period_id", votingPeriod.id)
              .eq("user_id", user.user.id)
              .maybeSingle() as any);

            if (existingUserVote) {
              localHasVoted = true;
              userVotedFor = existingUserVote.player_id;
            }
          }
        }

        // Set hasVoted state
        setHasVoted(localHasVoted);

        // If user has voted, get player details
        if (localHasVoted && userVotedFor) {
          const { data: player } = await (supabase
            .from("players")
            .select("name, jersey_number")
            .eq("id", userVotedFor)
            .maybeSingle() as any);

          if (player) {
            setVotedPlayer({
              name: player.name,
              jerseyNumber: player.jersey_number || undefined,
            });
          }
        }
      } catch (error) {
        console.error("Error checking voting status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkVotingStatus();
  }, []);

  const handleDismissBanner = () => {
    // Save the dismissal in localStorage
    const dismissedBanners = JSON.parse(localStorage.getItem("dismissedMvpBanners") || "{}");
    dismissedBanners[votingPeriodId as string] = true;
    localStorage.setItem("dismissedMvpBanners", JSON.stringify(dismissedBanners));

    // Hide the banner
    setIsBannerHidden(true);
  };

  if (isLoading || isBannerHidden) return null;

  if (hasVoted && votedPlayer) {
    return (
      <div className="mb-6 overflow-hidden">
        <div className="relative rounded-xl bg-gray-100 dark:bg-card border border-gray-100 dark:border-blue-900 shadow-md pt-3 pb-3">
          {/* Close button - deutlich in der Ecke positioniert */}
          <button
            onClick={handleDismissBanner}
            className="absolute top-3 right-3 p-1.5 rounded-full text-blue-500 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
            aria-label="Banner schließen"
          >
            <X className="h-4 w-4" />
          </button>

          <div className="p-5 flex items-center gap-4">
            {/* Trophy icon with success checkmark */}
            <div className="hidden sm:flex flex-shrink-0 items-center justify-center h-14 w-14 rounded-full bg-blue-500 dark:bg-blue-600 shadow-sm text-white">
              <div className="relative">
                <Trophy className="h-7 w-7" />
                <div className="absolute -bottom-1 -right-1 bg-white dark:bg-blue-300 rounded-full p-0.5 shadow-sm">
                  <Check className="h-3.5 w-3.5 text-blue-600 dark:text-blue-700" />
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {/* Mobile trophy */}
                <div className="sm:hidden flex-shrink-0 flex items-center justify-center h-9 w-9 rounded-full bg-blue-500 dark:bg-blue-600 shadow-sm text-white">
                  <div className="relative">
                    <Trophy className="h-5 w-5" />
                    <div className="absolute -bottom-1 -right-1 bg-white dark:bg-blue-300 rounded-full p-0.5 shadow-sm">
                      <Check className="h-2.5 w-2.5 text-blue-600 dark:text-blue-700" />
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-blue-800 dark:text-blue-300">
                  MVP-Abstimmung abgeschlossen!
                </h3>
              </div>
              <p className="text-blue-700 dark:text-blue-300 mt-1">
                Du hast für{" "}
                <span className="font-semibold">
                  {votedPlayer.name}
                  {votedPlayer.jerseyNumber ? ` #${votedPlayer.jerseyNumber}` : ""}
                </span>{" "}
                als MVP{gameDate ? ` am ${gameDate}` : ""} abgestimmt. Danke für deine Teilnahme!
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 overflow-hidden">
      <div className="relative rounded-xl bg-gray-100 dark:bg-card border border-gray-100 dark:border-blue-900 shadow-md pt-3 pb-3">
        {/* Close button - deutlich in der Ecke positioniert */}
        <button
          onClick={handleDismissBanner}
          className="absolute top-3 right-3 p-1.5 rounded-full text-blue-500 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors z-10"
          aria-label="Banner schließen"
        >
          <X className="h-4 w-4" />
        </button>

        <div className="p-5 flex flex-col sm:flex-row items-center gap-4">
          {/* Trophy icon */}
          <div className="hidden sm:flex flex-shrink-0 items-center justify-center h-16 w-16 rounded-full bg-blue-600 dark:bg-blue-700 shadow-sm text-white">
            <Trophy className="h-8 w-8" />
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {/* Mobile trophy */}
              <div className="sm:hidden flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-blue-600 dark:bg-blue-700 shadow-sm text-white">
                <Trophy className="h-5 w-5" />
              </div>

              <h3 className="text-xl font-bold text-blue-800 dark:text-blue-300">
                MVP Abstimmung ist geöffnet!
              </h3>
            </div>
            <p className="text-blue-700 dark:text-blue-300 mb-4 sm:mb-0">
              Stimme jetzt für den wertvollsten Spieler des letzten Spiels
              {gameDate ? ` (${gameDate})` : ""} ab!
            </p>
          </div>

          {/* Button */}
          <div className="w-full sm:w-auto flex-shrink-0">
            <Button
              className={cn(
                "w-full sm:w-auto rounded-lg bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600",
                "border-0 shadow-sm text-white font-medium py-3 h-auto px-4"
              )}
              onClick={() => navigate("/mvp-voting")}
            >
              <span className="flex items-center gap-1">Jetzt abstimmen</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
