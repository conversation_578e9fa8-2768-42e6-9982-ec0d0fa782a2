import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  Home,
  History,
  Shield,
  LogOut,
  Moon,
  Sun,
  Laptop,
  Award,
  BarChart,
  MessageSquare,
} from "lucide-react";
import { useTheme } from "@/components/theme-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderMenuProps {
  onLogout: () => void;
  activePage?: string;
}

export function HeaderMenu({ onLogout, activePage }: HeaderMenuProps) {
  const { theme, setTheme } = useTheme();

  const navItems = [
    { id: "home", icon: <Home className="h-5 w-5" />, label: "Startseite", path: "/" },
    { id: "history", icon: <History className="h-5 w-5" />, label: "Historie", path: "/history" },
    {
      id: "statistics",
      icon: <BarChart className="h-5 w-5" />,
      label: "Statistiken",
      path: "/statistics",
    },
    { id: "mvp-history", icon: <Award className="h-5 w-5" />, label: "MVP", path: "/mvp-history" },
    {
      id: "feedback",
      icon: <MessageSquare className="h-5 w-5" />,
      label: "Feedback",
      path: "/feedback",
    },
  ];

  const adminItems = [
    { id: "admin", icon: <Shield className="h-5 w-5" />, label: "Admin Panel", path: "/admin" },
  ];

  const getThemeIcon = () => {
    switch (theme) {
      case "dark":
        return <Moon className="h-5 w-5" />;
      case "light":
        return <Sun className="h-5 w-5" />;
      default:
        return <Laptop className="h-5 w-5" />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case "dark":
        return "Dunkles Theme";
      case "light":
        return "Helles Theme";
      default:
        return "System Theme";
    }
  };

  return (
    <div className="flex flex-col h-full justify-between py-4 px-2">
      {/* Main Navigation */}
      <div>
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.id}>
              <Link to={item.path}>
                <Button
                  variant={activePage === item.id ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start px-4",
                    activePage === item.id
                      ? "bg-team-primary text-white hover:bg-team-accent hover:text-white dark:bg-team-primary/80"
                      : "dark:text-gray-200 dark:hover:bg-muted"
                  )}
                >
                  {item.icon}
                  <span className="ml-3">{item.label}</span>
                </Button>
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Admin and Logout Section */}
      <div>
        <Separator className="my-4" />
        <ul className="space-y-1">
          {adminItems.map((item) => (
            <li key={item.id}>
              <Link to={item.path}>
                <Button
                  variant={activePage === item.id ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start px-4",
                    activePage === item.id
                      ? "bg-team-primary text-white hover:bg-team-accent hover:text-white dark:bg-team-primary/80"
                      : "dark:text-gray-200 dark:hover:bg-muted"
                  )}
                >
                  {item.icon}
                  <span className="ml-3">{item.label}</span>
                </Button>
              </Link>
            </li>
          ))}

          {/* Theme Selection */}
          <li>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-between px-4 dark:text-gray-200 dark:hover:bg-muted"
                >
                  <div className="flex items-center">
                    {getThemeIcon()}
                    <span className="ml-5">{getThemeLabel()}</span>
                  </div>
                  <svg
                    className="h-4 w-4 opacity-50"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-[200px] dark:bg-card dark:border-border"
              >
                <DropdownMenuItem
                  onClick={() => setTheme("light")}
                  className="dark:hover:bg-muted dark:focus:bg-muted"
                >
                  <Sun className="mr-2 h-4 w-4" />
                  <span>Hell</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setTheme("dark")}
                  className="dark:hover:bg-muted dark:focus:bg-muted"
                >
                  <Moon className="mr-2 h-4 w-4" />
                  <span>Dunkel</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setTheme("system")}
                  className="dark:hover:bg-muted dark:focus:bg-muted"
                >
                  <Laptop className="mr-2 h-4 w-4" />
                  <span>System</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </li>

          <li>
            <Button
              variant="ghost"
              className="w-full justify-start px-4 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-950/50"
              onClick={onLogout}
            >
              <LogOut className="h-5 w-5" />
              <span className="ml-3">Abmelden</span>
            </Button>
          </li>
        </ul>
      </div>
    </div>
  );
}

export default HeaderMenu;
