"use client";

import { usePlayers } from "@/hooks/usePlayers";
import { useGameSessions } from "@/hooks/useGameSessions";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useEffect, useState } from "react";
import { Player } from "@/types";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { PlayerName } from "@/components/player-profile/PlayerName";

interface PlayerWithRate extends Player {
  participationCount: number;
  participationRate: number; // Prozentsatz
}

export default function ParticipationRate() {
  const { players } = usePlayers();
  const { pastSessions, loading } = useGameSessions();
  const [topRatePlayers, setTopRatePlayers] = useState<PlayerWithRate[]>([]);
  const [playedSessionsCount, setPlayedSessionsCount] = useState(0);

  useEffect(() => {
    // Nur berechnen, wenn wir vergangene Sessions mit Teams-Daten haben
    if (!pastSessions || pastSessions.length === 0) {
      console.log("Keine vergangenen Sessions für Teilnahmequote-Berechnung verfügbar");
      setTopRatePlayers([]);
      setPlayedSessionsCount(0);
      return;
    }

    // Filter out cancelled and archived sessions - only count played games
    const playedSessions = pastSessions.filter((session) => session.status === "played");

    if (playedSessions.length === 0) {
      console.log("Keine gespielten Sessions für Teilnahmequote-Berechnung verfügbar");
      setTopRatePlayers([]);
      setPlayedSessionsCount(0);
      return;
    }

    setPlayedSessionsCount(playedSessions.length);
    console.log("Berechne Teilnahmequoten aus gespielten Sessions:", playedSessions.length);

    // Teilnahmen für jeden Spieler zählen
    const playerParticipation = new Map<string, number>();
    let anyTeamsFound = false;

    playedSessions.forEach((session) => {
      if (session.teams && session.teams.length > 0) {
        anyTeamsFound = true;
        session.teams.forEach((team) => {
          if (team.players && team.players.length > 0) {
            team.players.forEach((player) => {
              if (player.id) {
                const currentCount = playerParticipation.get(player.id) || 0;
                playerParticipation.set(player.id, currentCount + 1);
              }
            });
          }
        });
      }
    });

    if (!anyTeamsFound) {
      console.log("Keine Teams in gespielten Sessions gefunden");
      setTopRatePlayers([]);
      return;
    }

    // Spieler-Array mit Statistiken erstellen
    const playersWithRates: PlayerWithRate[] = players
      .map((player) => {
        const participationCount = playerParticipation.get(player.id) || 0;

        // Teilnahmequote berechnen (%) - nur basierend auf gespielten Sessions
        const participationRate =
          playedSessions.length > 0
            ? Math.round((participationCount / playedSessions.length) * 100)
            : 0;

        return {
          ...player,
          participationCount,
          participationRate,
        };
      })
      // Nur Spieler einschließen, die teilgenommen haben
      .filter((player) => player.participationCount > 0);

    // Nach Teilnahmequote (absteigend) sortieren und die Top 5 auswählen
    const sorted = playersWithRates
      .sort((a, b) => {
        // Primär nach Teilnahmequote sortieren
        if (b.participationRate !== a.participationRate) {
          return b.participationRate - a.participationRate;
        }
        // Bei gleicher Quote, nach Anzahl der Teilnahmen sortieren
        return b.participationCount - a.participationCount;
      })
      .slice(0, 5);

    console.log("Spieler mit höchster Teilnahmequote berechnet:", sorted);
    setTopRatePlayers(sorted);
  }, [players, pastSessions]);

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Teilnahmequote</CardTitle>
              <CardDescription>Durchschnittliche Teilnahme der Spieler</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex flex-col items-center justify-center h-full">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 mt-4">Lade Spielerstatistiken...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (topRatePlayers.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Teilnahmequote</CardTitle>
              <CardDescription>Durchschnittliche Teilnahme der Spieler</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 dark:text-gray-400">
                Noch keine Spielerstatistiken verfügbar
              </p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Teilnahmequote</CardTitle>
            <CardDescription>Durchschnittliche Teilnahme der Spieler</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3">
          <div className="space-y-3">
            {topRatePlayers.map((player, index) => (
              <div key={player.id} className="space-y-1.5">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-7 w-7">
                      <AvatarFallback className="text-xs">
                        {player.jerseyNumber
                          ? "#" + player.jerseyNumber
                          : player.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <PlayerName
                      player={player}
                      playerName={player.name}
                      className="font-medium text-sm hover:text-blue-600 dark:hover:text-blue-400"
                    />
                  </div>
                  <div className="text-sm font-medium">{player.participationRate}%</div>
                </div>
                <Progress
                  value={player.participationRate}
                  className="h-2"
                  indicatorClassName={
                    player.participationRate > 80
                      ? "bg-green-500 dark:bg-green-600"
                      : player.participationRate > 50
                        ? "bg-blue-500 dark:bg-blue-600"
                        : "bg-gray-500 dark:bg-gray-600"
                  }
                />
                <div className="text-xs text-muted-foreground text-right">
                  {player.participationCount} von {playedSessionsCount} Spielen
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
