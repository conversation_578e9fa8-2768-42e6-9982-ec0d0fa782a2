import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useGameSessions } from "@/hooks/useGameSessions";
import { formatDate, formatDateOnlyDate } from "@/utils/dateUtils";
import { useEffect, useState, useRef, useMemo } from "react";
import { GameSession } from "@/types";
import {
  Clock,
  BarChart,
  Calendar,
  Users,
  Trophy,
  Flag,
  Award,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  ChevronDown,
  Search,
  SlidersHorizontal,
  ChevronUp,
} from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/custom-card";
import { Separator } from "../ui/separator";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  Pa<PERSON>ationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { useLocation, useSearchParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { PlayerName } from "@/components/player-profile/PlayerName";
import { useMatchResults, getOverallWinner } from "@/hooks/useMatchResults";
import { MatchResult } from "@/types/match-results";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { fetchMvpWinners, MVPWinner } from "@/services/mvpService";
import { CustomPagination } from "@/components/ui/custom-pagination";
import { MatchResultsDisplay } from "./MatchResultsDisplay";
import { TeamDisplay } from "./TeamDisplay";
import { Avatar, AvatarFallback } from "../ui/avatar";

// Save view mode preference to localStorage
const saveViewModePreference = (mode: "cards" | "list") => {
  localStorage.setItem("matchHistoryViewMode", mode);
};

interface MatchHistoryProps {
  isAdminView?: boolean;
}

export default function MatchHistory({ isAdminView = false }: MatchHistoryProps) {
  const [viewMode, setViewMode] = useState<"cards" | "list">(() => {
    const stored = localStorage.getItem("matchHistoryViewMode");
    return stored === "list" ? "list" : "cards";
  });
  const MATCHES_PER_PAGE = 6;
  const { pastSessions, loading } = useGameSessions({
    fetchCurrent: false,
    fetchPast: true,
  });
  const [sessions, setSessions] = useState<GameSession[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchParams] = useSearchParams();
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [mvpWinners, setMvpWinners] = useState<MVPWinner[]>([]);
  const [loadingMvp, setLoadingMvp] = useState(false);
  const highlightedItemRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const hasScrolled = useRef<boolean>(false);

  // New state variables for enhanced UI
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Get gameId from URL params (when navigating from MVP History)
  const gameId = searchParams.get("gameId");

  // Liste der aktiven Session-IDs für das Abrufen der Spielergebnisse
  const sessionIds = useMemo(() => {
    const paginatedSessions = sessions.slice(
      (currentPage - 1) * MATCHES_PER_PAGE,
      currentPage * MATCHES_PER_PAGE
    );
    return paginatedSessions
      .filter((session) => session.id && session.status === "played")
      .map((session) => session.id || "")
      .filter((id) => id !== "");
  }, [sessions, currentPage]);

  // Spielergebnisse abrufen
  const { results: matchResults, loading: loadingResults } = useMatchResults(sessionIds);

  // Fetch MVP winners for visible games
  const fetchMvpData = async (gameSessions: GameSession[]) => {
    setLoadingMvp(true);
    try {
      const sessionIdsToFetch = gameSessions
        .filter((session) => session.id)
        .map((session) => session.id as string);

      const winners = await fetchMvpWinners(sessionIdsToFetch);
      setMvpWinners(winners);
    } catch (error) {
      console.error("Error fetching MVP winners:", error);
    } finally {
      setLoadingMvp(false);
    }
  };

  useEffect(() => {
    if (pastSessions && pastSessions.length > 0) {
      setSessions(pastSessions);
      setTotalPages(Math.ceil(pastSessions.length / MATCHES_PER_PAGE));

      // If there's a gameId parameter, find that session and set it as active
      if (gameId) {
        const sessionIndex = pastSessions.findIndex((session) => session.id === gameId);
        if (sessionIndex !== -1) {
          setActiveSessionId(gameId);

          // Calculate which page this session is on
          const sessionPage = Math.floor(sessionIndex / MATCHES_PER_PAGE) + 1;
          setCurrentPage(sessionPage);

          // Reset scroll flag when game ID changes
          hasScrolled.current = false;
        }
      }
    } else {
      setSessions([]);
      setTotalPages(1);
    }
  }, [pastSessions, gameId]);

  // Fetch MVP winners whenever the page changes or sessions are loaded
  useEffect(() => {
    if (paginatedSessions.length > 0) {
      fetchMvpData(paginatedSessions);
    }
  }, [currentPage, sessions.length]);

  // Enhanced scrolling effect for highlighted item
  useEffect(() => {
    // Only scroll if we have an active session and haven't scrolled yet
    if (activeSessionId && !hasScrolled.current) {
      // Wait a bit for rendering to complete
      const scrollTimer = setTimeout(() => {
        if (highlightedItemRef.current) {
          // Scroll the highlighted item into view
          highlightedItemRef.current.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });

          // Mark that we've scrolled to avoid repeated scrolling
          hasScrolled.current = true;
        } else if (sectionRef.current) {
          // If the highlighted item isn't rendered yet, scroll to the section itself
          sectionRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 500); // Slightly longer timeout to ensure content is rendered

      return () => clearTimeout(scrollTimer);
    }
  }, [activeSessionId, currentPage, loading]);

  // Filter sessions based on searchTerm
  const filteredSessions = useMemo(() => {
    if (!searchTerm.trim()) return sessions;

    const searchLower = searchTerm.toLowerCase();
    return sessions.filter((session) => {
      // Search by date
      const dateStr = session.date ? formatDateOnlyDate(session.date) : "";
      if (dateStr.toLowerCase().includes(searchLower)) {
        return true;
      }

      // Search by MVP name
      const mvpWinner = mvpWinners.find((winner) => winner.matchId === session.id);
      if (mvpWinner && mvpWinner.playerName.toLowerCase().includes(searchLower)) {
        return true;
      }

      // Search by team player names
      if (session.teams) {
        return session.teams.some((team) =>
          team.players?.some((player) => player.name.toLowerCase().includes(searchLower))
        );
      }

      return false;
    });
  }, [sessions, searchTerm, mvpWinners]);

  // Sort sessions by date
  const sortedSessions = useMemo(() => {
    return [...filteredSessions].sort((a, b) => {
      const dateA = a.date ? new Date(a.date).getTime() : 0;
      const dateB = b.date ? new Date(b.date).getTime() : 0;

      return sortOrder === "desc" ? dateB - dateA : dateA - dateB;
    });
  }, [filteredSessions, sortOrder]);

  // Calculate total pages based on filtered/sorted sessions
  useEffect(() => {
    setTotalPages(Math.ceil(sortedSessions.length / MATCHES_PER_PAGE));
    // Reset to page 1 when filter changes
    if (currentPage > 1 && sortedSessions.length <= MATCHES_PER_PAGE) {
      setCurrentPage(1);
    }
  }, [sortedSessions.length, currentPage]);

  // Get paginated sessions for current page
  const paginatedSessions = useMemo(() => {
    const startIndex = (currentPage - 1) * MATCHES_PER_PAGE;
    const endIndex = startIndex + MATCHES_PER_PAGE;
    return sortedSessions.slice(startIndex, endIndex);
  }, [sortedSessions, currentPage, MATCHES_PER_PAGE]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);

    // Reset scroll flag when changing pages
    if (activeSessionId) {
      hasScrolled.current = false;
    }
  };

  // Helper to find the MVP winners for a specific match
  const getMvpWinnersForMatch = (sessionId: string) => {
    return mvpWinners.filter((winner) => winner.matchId === sessionId);
  };

  // Helper to check if a player is an MVP for a specific match
  const isPlayerMvp = (playerName: string, sessionId: string) => {
    const winners = getMvpWinnersForMatch(sessionId);
    return winners.some((winner) => winner.playerName === playerName);
  };

  // Helper-Funktion zum Erstellen einer Team-ID zu Team-Name Map
  const createTeamMap = (session: GameSession): Record<string, string> => {
    if (!session.teams || session.teams.length === 0) return {};

    const teamMap: Record<string, string> = {};
    session.teams.forEach((team) => {
      if (team.id) {
        // Ensure we always have a meaningful team name
        teamMap[team.id] = team.name || `Team ${team.id.substring(0, 4)}`;
      }
    });

    return teamMap;
  };

  // Update localStorage when view mode changes
  const handleViewModeChange = (mode: "cards" | "list") => {
    setViewMode(mode);
    saveViewModePreference(mode);
  };

  if (loading) {
    return (
      <section className="w-full">
        <div className="flex flex-col items-center justify-center py-12">
          <LoadingSpinner size="lg" />
          <p className="text-muted-foreground mt-4">Lade Spielverlauf...</p>
        </div>
      </section>
    );
  }

  return (
    <section ref={sectionRef} className="w-full" id="match-history-section">
      <Card className="w-full dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Spielhistorie</CardTitle>
            <CardDescription>
              Übersicht aller vergangenen Spiele und deren Ergebnisse
            </CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3">
          {/* Search and filter controls */}
          <div className="flex items-center gap-2 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Suche nach Datum, Spieler oder MVP..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-10 w-10">
                    <SlidersHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setSortOrder("desc")}>
                    Neueste zuerst
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortOrder("asc")}>
                    Älteste zuerst
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="outline"
                size="icon"
                className={`h-10 w-10 ${viewMode === "cards" ? "bg-muted" : ""}`}
                onClick={() => handleViewModeChange("cards")}
                title="Karten-Ansicht"
              >
                <div className="grid grid-cols-2 gap-0.5">
                  <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                  <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                  <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                  <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                </div>
              </Button>

              <Button
                variant="outline"
                size="icon"
                className={`h-10 w-10 ${viewMode === "list" ? "bg-muted" : ""}`}
                onClick={() => handleViewModeChange("list")}
                title="Listen-Ansicht"
              >
                <div className="flex flex-col gap-0.5 items-center justify-center">
                  <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                  <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                  <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                </div>
              </Button>
            </div>
          </div>

          {/* No results */}
          {paginatedSessions.length === 0 ? (
            <div className="text-center py-16">
              <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-500/30 dark:text-blue-700/30" />
              <h3 className="text-xl font-medium mb-2 dark:text-white">Keine Spiele gefunden</h3>
              {searchTerm ? (
                <p className="text-muted-foreground dark:text-zinc-400">
                  Keine Ergebnisse für "{searchTerm}". Versuche einen anderen Suchbegriff.
                </p>
              ) : (
                <p className="text-muted-foreground dark:text-zinc-400">
                  Es wurden noch keine Spiele gespielt.
                </p>
              )}
            </div>
          ) : viewMode === "cards" ? (
            /* Card View */
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {paginatedSessions.map((session) => {
                const mvpWinners = getMvpWinnersForMatch(session.id || "");
                const teamCount = session.teams?.length || 0;
                const playerCount = session.playerCount || 0;

                return (
                  <Card
                    key={session.id}
                    className={cn(
                      "w-full overflow-hidden transition-all duration-200 hover:shadow-md dark:bg-zinc-900 dark:border-zinc-800 flex flex-col",
                      session.id === activeSessionId
                        ? "ring-1 ring-blue-500 dark:ring-blue-600"
                        : ""
                    )}
                    ref={session.id === activeSessionId ? highlightedItemRef : undefined}
                  >
                    <CardHeader className="p-4 pb-2">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex flex-wrap gap-2">
                          <Badge
                            variant="secondary"
                            className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
                          >
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDateOnlyDate(session.date)}
                          </Badge>

                          {session.status === "cancelled" && (
                            <Badge className="bg-red-100 hover:bg-red-200 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-none">
                              Abgesagt
                            </Badge>
                          )}

                          {/* MVP Badge */}
                          {getMvpWinnersForMatch(session.id || "").length > 0 && (
                            <Badge className="bg-blue-100 hover:bg-blue-200 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-none flex items-center gap-1">
                              <Trophy className="h-3 w-3" />
                              <span>
                                {getMvpWinnersForMatch(session.id || "").length > 1
                                  ? "MVPs"
                                  : "MVP"}
                              </span>
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-4 pt-0 flex-1">
                      <div className="flex flex-col gap-4 mt-2 mb-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="text-sm font-medium dark:text-white">
                              {playerCount} Spieler
                            </p>
                            <p className="text-xs text-muted-foreground dark:text-zinc-400">
                              {teamCount} Teams
                            </p>
                          </div>
                        </div>

                        {/* MVP Winners - Simple layout */}
                        {getMvpWinnersForMatch(session.id || "").length > 0 && (
                          <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-1.5 mb-1">
                              <Trophy className="h-3.5 w-3.5 text-blue-500" />
                              <span className="text-xs text-zinc-500 dark:text-zinc-400 font-medium">
                                {getMvpWinnersForMatch(session.id || "").length > 1
                                  ? "MVPs"
                                  : "MVP"}
                              </span>
                            </div>
                            {getMvpWinnersForMatch(session.id || "").map((winner) => (
                              <div key={winner.playerId} className="flex items-center gap-3">
                                <Avatar className="h-10 w-10 bg-gradient-to-br from-blue-400 to-blue-600 border-2 border-blue-100 dark:border-blue-900/30">
                                  <AvatarFallback className="text-sm font-bold text-gray-800 dark:text-gray-200">
                                    {winner.jerseyNumber
                                      ? "#" + winner.jerseyNumber
                                      : winner.playerName.substring(0, 2).toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <PlayerName
                                    playerName={winner.playerName}
                                    playerId={winner.playerId}
                                    className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400"
                                  />
                                  {/* {winner.voteCount && (
                                      <p className="text-xs text-muted-foreground">
                                        {winner.voteCount} Stimmen
                                      </p>
                                    )} */}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>

                    <CardFooter className="p-3 border-t bg-muted/30 dark:border-zinc-800 dark:bg-zinc-800/30 mt-auto">
                      <Button
                        variant="ghost"
                        className="w-full text-sm text-muted-foreground hover:text-foreground dark:text-zinc-400 dark:hover:text-white"
                        onClick={() =>
                          setActiveSessionId(session.id === activeSessionId ? null : session.id)
                        }
                      >
                        {session.id === activeSessionId ? (
                          <>
                            <ChevronUp className="h-4 w-4 mr-2" />
                            Details ausblenden
                          </>
                        ) : (
                          <>
                            <ChevronDown className="h-4 w-4 mr-2" />
                            Details anzeigen
                          </>
                        )}
                      </Button>
                    </CardFooter>

                    {/* Expanded Content */}
                    {session.id === activeSessionId && (
                      <div className="p-4 border-t border-gray-100 dark:border-zinc-800 bg-gray-50 dark:bg-zinc-800/20">
                        {/* Teams */}
                        {session.teams && session.teams.length > 0 ? (
                          <div className="space-y-4">
                            <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                              Teams
                            </h3>
                            <TeamDisplay
                              teams={session.teams}
                              isAdminView={isAdminView}
                              sessionId={session.id || ""}
                              mvpPlayerNames={getMvpWinnersForMatch(session.id || "").map(
                                (winner) => winner.playerName
                              )}
                            />
                          </div>
                        ) : (
                          <div className="text-center p-4 text-muted-foreground">
                            Keine Teams verfügbar
                          </div>
                        )}

                        {/* Match Results */}
                        {session.status === "played" && session.id && (
                          <div className="mt-4 pt-4 border-t border-gray-100 dark:border-zinc-700">
                            <h3 className="font-medium text-gray-900 dark:text-white text-sm mb-3">
                              Spielergebnisse
                            </h3>
                            <MatchResultsDisplay
                              sessionId={session.id}
                              teamMap={createTeamMap(session)}
                              results={matchResults}
                              loading={loadingResults}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </Card>
                );
              })}
            </div>
          ) : (
            /* List View */
            <Card className="dark:bg-zinc-900 dark:border-zinc-800">
              <CardContent className="p-0">
                <div className="divide-y dark:divide-zinc-800">
                  {paginatedSessions.map((session) => {
                    const mvpWinners = getMvpWinnersForMatch(session.id || "");
                    const teamMap = createTeamMap(session);
                    const isExpanded = session.id === activeSessionId;

                    return (
                      <div
                        key={session.id}
                        className={cn(
                          "transition-all duration-200 overflow-hidden",
                          isExpanded
                            ? "bg-gray-50 dark:bg-zinc-800/20"
                            : "hover:bg-gray-50/50 dark:hover:bg-zinc-800/10",
                          session.id === activeSessionId && !isExpanded
                            ? "ring-1 ring-blue-500 dark:ring-blue-600"
                            : ""
                        )}
                        ref={session.id === activeSessionId ? highlightedItemRef : null}
                      >
                        {/* Session Header */}
                        <div
                          className="p-4 sm:p-6 cursor-pointer flex items-center justify-between"
                          onClick={() => setActiveSessionId(isExpanded ? null : session.id)}
                        >
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6">
                            {/* Date */}
                            <div className="text-lg font-medium text-gray-900 dark:text-white">
                              {formatDateOnlyDate(session.date)}
                            </div>

                            {/* Badges */}
                            <div className="flex flex-wrap gap-2">
                              {session.status === "cancelled" && (
                                <Badge className="bg-red-100 hover:bg-red-200 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-none">
                                  Abgesagt
                                </Badge>
                              )}

                              {session.status === "archived" && (
                                <Badge className="bg-amber-100 hover:bg-amber-200 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 border-none">
                                  Archiviert
                                </Badge>
                              )}

                              {session.status !== "played" &&
                                session.status !== "cancelled" &&
                                session.status !== "archived" && (
                                  <Badge
                                    variant="outline"
                                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-zinc-800 dark:text-zinc-400 border-none"
                                  >
                                    Geplant
                                  </Badge>
                                )}

                              {/* MVP Badge */}
                              {getMvpWinnersForMatch(session.id || "").length > 0 && (
                                <Badge className="bg-blue-100 hover:bg-blue-200 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-none flex items-center gap-1">
                                  <Trophy className="h-3 w-3" />
                                  <span>
                                    {getMvpWinnersForMatch(session.id || "").length > 1
                                      ? "MVPs"
                                      : "MVP"}
                                  </span>
                                </Badge>
                              )}
                            </div>
                          </div>

                          <ChevronDown
                            className={cn(
                              "h-5 w-5 text-gray-400 transition-transform duration-200",
                              isExpanded ? "transform rotate-180" : ""
                            )}
                          />
                        </div>

                        {/* Session Details - Expanded View */}
                        {isExpanded && (
                          <div className="px-4 sm:px-6 pb-6 space-y-6 pt-0 border-t border-gray-100 dark:border-zinc-800">
                            <div className="mt-4">
                              {/* Session Info */}
                              <div className="flex flex-col gap-4 bg-white dark:bg-zinc-900 rounded-lg p-3 shadow-sm border border-gray-100 dark:border-zinc-800">
                                <div className="flex items-center text-sm">
                                  <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                  </div>
                                  <span className="text-gray-700 dark:text-zinc-300">
                                    {formatDate(session.date)}
                                  </span>
                                </div>
                                <div className="flex items-center text-sm">
                                  <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-3">
                                    <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
                                  </div>
                                  <span className="text-gray-700 dark:text-zinc-300">
                                    Spieler: {session.playerCount || 0}
                                  </span>
                                </div>
                                {getMvpWinnersForMatch(session.id || "").length > 0 && (
                                  <div className="flex items-center text-sm">
                                    <div className="h-8 w-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                                      <Trophy className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                                    </div>
                                    <span className="text-gray-700 dark:text-zinc-300">
                                      {getMvpWinnersForMatch(session.id || "").length > 1
                                        ? "MVPs: "
                                        : "MVP: "}
                                      {getMvpWinnersForMatch(session.id || "").map(
                                        (winner, index, array) => (
                                          <span key={winner.playerId}>
                                            <PlayerName
                                              playerName={winner.playerName}
                                              playerId={winner.playerId}
                                              jerseyNumber={winner.jerseyNumber}
                                              className="font-semibold text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"
                                            />
                                            {index < array.length - 1 && ", "}
                                          </span>
                                        )
                                      )}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Teams Overview */}
                            {session.teams && session.teams.length > 0 ? (
                              <div className="space-y-3">
                                <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                                  Teams
                                </h3>
                                <TeamDisplay
                                  teams={session.teams}
                                  isAdminView={isAdminView}
                                  sessionId={session.id || ""}
                                  mvpPlayerNames={getMvpWinnersForMatch(session.id || "").map(
                                    (winner) => winner.playerName
                                  )}
                                />
                              </div>
                            ) : (
                              <div className="p-4 text-center bg-gray-50 dark:bg-zinc-800/50 rounded-lg border border-gray-100 dark:border-zinc-800">
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  Keine Teams verfügbar
                                </span>
                              </div>
                            )}

                            {/* Match Results */}
                            {session.status === "played" && session.id && (
                              <>
                                <div className="space-y-4">
                                  <h3 className="text-base font-medium text-gray-900 dark:text-white">
                                    Spielergebnisse
                                  </h3>
                                  <MatchResultsDisplay
                                    sessionId={session.id}
                                    teamMap={teamMap}
                                    results={matchResults}
                                    loading={loadingResults}
                                  />
                                </div>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="border-t border-gray-100 dark:border-zinc-800 mt-6 pt-6">
              <CustomPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </section>
  );
}
