import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/custom-card";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Users, UserX, Trophy, TrendingUp, Clock, Target, Activity } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";
import {
  Bar,
  BarChart,
  CartesianGrid,
  XAxis,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  YAxis,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { type ChartConfig } from "@/components/ui/chart";

// Types
interface Stats {
  games: {
    total: number;
    played: number;
    cancelled: number;
    lastDate: string | null;
    teamSizes: { size2: number; size3: number; size4: number };
    monthlyTrend: Array<{ month: string; count: number }>;
  };
  players: {
    total: number;
    inactive: number;
    nonResponsive: number;
  };
}

// Chart configurations
const CHART_CONFIGS = {
  gameStatus: {
    played: { label: "Gespielt", color: "hsl(var(--chart-2))" },
    scheduled: { label: "Geplant", color: "hsl(var(--chart-1))" },
    cancelled: { label: "Abgebrochen", color: "hsl(var(--chart-3))" },
  } satisfies ChartConfig,

  teamSizes: {
    size2: { label: "2er Teams", color: "hsl(var(--chart-1))" },
    size3: { label: "3er Teams", color: "hsl(var(--chart-2))" },
    size4: { label: "4er Teams", color: "hsl(var(--chart-3))" },
  } satisfies ChartConfig,

  trend: {
    count: { label: "Spiele", color: "hsl(var(--chart-1))" },
  } satisfies ChartConfig,
};

// Reusable KPI Card Component
interface KPICardProps {
  title: string;
  value: number | string;
  subtitle: string;
  icon: React.ReactNode;
  valueColor?: string;
}

function KPICard({ title, value, subtitle, icon, valueColor = "text-primary" }: KPICardProps) {
  return (
    <Card className="dark:bg-zinc-900/50">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className={`text-3xl font-bold ${valueColor}`}>{value}</p>
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          </div>
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Chart Card Component
interface ChartCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

function ChartCard({ title, description, icon, children }: ChartCardProps) {
  return (
    <Card className="dark:bg-zinc-900/50">
      <CardHeader>
        <div className="space-y-1">
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

export default function AdminStatistics() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAllStatistics();
  }, []);

  const fetchAllStatistics = async () => {
    try {
      setLoading(true);
      const [gameStats, playerStats] = await Promise.all([
        fetchGameStatistics(),
        fetchPlayerStatistics(),
      ]);

      setStats({
        games: gameStats,
        players: playerStats,
      });
    } catch (error) {
      console.error("Error fetching statistics:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGameStatistics = async () => {
    const { data: sessions } = await supabase
      .from("game_sessions")
      .select("*")
      .order("date", { ascending: false });

    const { data: teams } = await supabase.from("teams").select("game_session_id");

    const total = sessions?.length || 0;
    const played = sessions?.filter((s) => s.status === "played").length || 0;
    const cancelled = sessions?.filter((s) => s.status === "cancelled").length || 0;
    const lastDate = sessions?.find((s) => s.status === "played")?.date || null;

    // Team size distribution
    const teamsBySession =
      teams?.reduce(
        (acc, team) => {
          acc[team.game_session_id] = (acc[team.game_session_id] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    const teamSizes = Object.values(teamsBySession).reduce(
      (acc, count) => {
        if (count === 2) acc.size2++;
        else if (count === 3) acc.size3++;
        else if (count === 4) acc.size4++;
        return acc;
      },
      { size2: 0, size3: 0, size4: 0 }
    );

    // Monthly trend (last 6 months)
    const monthlyTrend = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const count =
        sessions?.filter((session) => {
          const sessionDate = new Date(session.date);
          return (
            sessionDate >= monthStart && sessionDate <= monthEnd && session.status === "played"
          );
        }).length || 0;

      return {
        month: date.toLocaleDateString("de-DE", { month: "short" }),
        count,
      };
    });

    return { total, played, cancelled, lastDate, teamSizes, monthlyTrend };
  };

  const fetchPlayerStatistics = async () => {
    const { data: players } = await supabase.from("players").select("*");
    const { data: recentSessions } = await supabase
      .from("game_sessions")
      .select("id")
      .eq("status", "played")
      .order("date", { ascending: false })
      .limit(4);

    const sessionIds = recentSessions?.map((s) => s.id) || [];
    const { data: signups } = await supabase
      .from("player_signups")
      .select("player_id")
      .in("game_session_id", sessionIds);

    const total = players?.length || 0;
    const inactive = players?.filter((player) => !player.is_active).length || 0;

    const responseCount =
      signups?.reduce(
        (acc, signup) => {
          acc[signup.player_id] = (acc[signup.player_id] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    const nonResponsive =
      players?.filter(
        (player) =>
          player.is_active && sessionIds.length >= 4 && (responseCount[player.id] || 0) === 0
      ).length || 0;

    return { total, inactive, nonResponsive };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!stats) return null;

  // Prepare chart data
  const gameStatusData = [
    { name: "played", value: stats.games.played, fill: "hsl(var(--chart-2))" },
    {
      name: "scheduled",
      value: stats.games.total - stats.games.played - stats.games.cancelled,
      fill: "hsl(var(--chart-1))",
    },
    { name: "cancelled", value: stats.games.cancelled, fill: "hsl(var(--chart-3))" },
  ];

  const teamSizeData = [
    { name: "size2", value: stats.games.teamSizes.size2, fill: "hsl(var(--chart-1))" },
    { name: "size3", value: stats.games.teamSizes.size3, fill: "hsl(var(--chart-2))" },
    { name: "size4", value: stats.games.teamSizes.size4, fill: "hsl(var(--chart-3))" },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Statistiken</h1>
          <p className="text-muted-foreground">Umfassende Analyse von Spielen und Spielern</p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <Activity className="h-4 w-4" />
          Live Dashboard
        </Badge>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <KPICard
          title="Geplante Spiele"
          value={stats.games.total}
          subtitle="Insgesamt erstellt"
          icon={<Calendar className="h-6 w-6 text-primary" />}
        />
        <KPICard
          title="Gespielte Spiele"
          value={stats.games.played}
          subtitle="Erfolgreich durchgeführt"
          icon={<Trophy className="h-6 w-6 text-blue-600" />}
          valueColor="text-blue-600"
        />
        <KPICard
          title="Abgebrochene Spiele"
          value={stats.games.cancelled}
          subtitle="Leider ausgefallen"
          icon={<UserX className="h-6 w-6 text-rose-600" />}
          valueColor="text-rose-600"
        />
        <KPICard
          title="Spieler Gesamt"
          value={stats.players.total}
          subtitle="Registrierte Mitglieder"
          icon={<Users className="h-6 w-6 text-primary" />}
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        <ChartCard
          title="Spiel-Status Verteilung"
          description="Übersicht aller geplanten, gespielten und abgebrochenen Spiele"
          icon={<Target className="h-5 w-5 text-primary" />}
        >
          <ChartContainer config={CHART_CONFIGS.gameStatus} className="h-[300px] w-full">
            <PieChart>
              <Pie
                data={gameStatusData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                nameKey="name"
              >
                {gameStatusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent nameKey="name" />} />
            </PieChart>
          </ChartContainer>
        </ChartCard>

        <ChartCard
          title="Team-Größen Verteilung"
          description="Anzahl der Spiele nach Team-Größe"
          icon={<Users className="h-5 w-5 text-primary" />}
        >
          <ChartContainer config={CHART_CONFIGS.teamSizes} className="h-[300px] w-full">
            <BarChart data={teamSizeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="value" fill="hsl(var(--chart-2))" radius={8} />
            </BarChart>
          </ChartContainer>
        </ChartCard>
      </div>

      {/* Charts Row 2 */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        <ChartCard
          title="Spiele Trend (6 Monate)"
          description="Entwicklung der gespielten Spiele über Zeit"
          icon={<TrendingUp className="h-5 w-5 text-primary" />}
        >
          <ChartContainer config={CHART_CONFIGS.trend} className="h-[300px] w-full">
            <LineChart data={stats.games.monthlyTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line
                type="monotone"
                dataKey="count"
                stroke="hsl(var(--chart-1))"
                strokeWidth={3}
                dot={{ fill: "hsl(var(--chart-1))", strokeWidth: 2, r: 6 }}
              />
            </LineChart>
          </ChartContainer>
        </ChartCard>

        <ChartCard
          title="Spieler Aktivität"
          description="Beteiligung und Reaktionsverhalten"
          icon={<Activity className="h-5 w-5 text-primary" />}
        >
          <div className="space-y-6">
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-3">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{stats.players.total}</div>
                <div className="text-sm text-muted-foreground">Spieler Gesamt</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-rose-600">{stats.players.inactive}</div>
                <div className="text-sm text-muted-foreground">Inaktiv</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {stats.players.nonResponsive}
                </div>
                <div className="text-sm text-muted-foreground">Keine Rückmeldung</div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-3">Letzte Spiele Rückmeldung</h4>
              <div className="text-xs text-muted-foreground mb-2">
                {stats.players.nonResponsive} Spieler haben seit 4 Spielen nicht reagiert
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{
                    width: `${Math.max(0, 100 - (stats.players.nonResponsive / stats.players.total) * 100)}%`,
                  }}
                />
              </div>
            </div>
          </div>
        </ChartCard>
      </div>

      {/* Last Game Info */}
      <Card className="dark:bg-zinc-900/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="h-14 w-14 rounded-full bg-primary/10 flex items-center justify-center">
                <Clock className="h-7 w-7 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Letztes Spiel</h3>
                <p className="text-2xl font-bold text-primary">
                  {stats.games.lastDate
                    ? formatDistanceToNow(new Date(stats.games.lastDate), {
                        addSuffix: true,
                        locale: de,
                      })
                    : "Kein Spiel gespielt"}
                </p>
                <p className="text-sm text-muted-foreground">Letzte Aktivität der Gruppe</p>
              </div>
            </div>
            {stats.games.lastDate && (
              <Badge variant="secondary">
                {new Date(stats.games.lastDate).toLocaleDateString("de-DE")}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
