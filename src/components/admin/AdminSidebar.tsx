import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  History,
  LogOut,
  Award,
  Shuffle,
  MessageSquare,
  Trophy,
  BarChart3,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useNavigate, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { ModeToggle } from "@/components/mode-toggle";

interface AdminSidebarProps {
  activePage: string;
  setActivePage: (page: string) => void;
  isSidebarOpen: boolean;
}

export default function AdminSidebar({
  activePage,
  setActivePage,
  isSidebarOpen,
}: AdminSidebarProps) {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      localStorage.removeItem("isAdminLoggedIn");
      navigate("/login");
      toast({
        title: "Abgemeldet",
        description: "Sie wurden erfolgreich abgemeldet",
      });
    } catch (error) {
      console.error("Logout error:", error);
      toast({
        title: "Fehler",
        description: "Abmeldung fehlgeschlagen",
        variant: "destructive",
      });
    }
  };

  const menuCategories = [
    {
      title: "HAUPTFUNKTIONEN",
      items: [
        { id: "dashboard", icon: <LayoutDashboard className="h-5 w-5" />, label: "Dashboard" },
        { id: "game-management", icon: <Calendar className="h-5 w-5" />, label: "Spielplanung" },
        {
          id: "player-management",
          icon: <Users className="h-5 w-5" />,
          label: "Spielerverwaltung",
        },
      ],
    },
    {
      title: "TEAM & SPIEL",
      items: [
        { id: "team-playground", icon: <Shuffle className="h-5 w-5" />, label: "Team Playground" },
        { id: "tournament-mode", icon: <Trophy className="h-5 w-5" />, label: "Turniermodus" },
        { id: "mvp-management", icon: <Award className="h-5 w-5" />, label: "MVP" },
        { id: "history", icon: <History className="h-5 w-5" />, label: "Historie" },
      ],
    },
    {
      title: "AUSWERTUNG",
      items: [{ id: "statistics", icon: <BarChart3 className="h-5 w-5" />, label: "Statistiken" }],
    },
    {
      title: "SYSTEM",
      items: [
        { id: "feedback", icon: <MessageSquare className="h-5 w-5" />, label: "Feedback" },
        { id: "settings", icon: <Settings className="h-5 w-5" />, label: "Einstellungen" },
      ],
    },
  ];

  return (
    <aside
      className={cn(
        "fixed left-0 top-0 h-full bg-white dark:bg-zinc-900 border-r dark:border-zinc-800 z-50 transition-all duration-300 transform",
        isSidebarOpen ? "w-64 translate-x-0" : "w-0 -translate-x-full lg:w-0"
      )}
    >
      <div
        className={cn(
          "flex flex-col h-full",
          isSidebarOpen ? "opacity-100" : "opacity-0 overflow-hidden"
        )}
      >
        <div className="h-16 px-4 flex items-center gap-3 border-b">
          <img src="/images/logo.png" alt="INTER NRW Logo" className="h-8 w-8" />
          <div>
            <h1 className="text-base font-semibold text-gray-900 dark:text-gray-100">
              Admin Panel
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">Verwaltung & Kontrolle</p>
          </div>
        </div>

        <nav className="flex-1 overflow-y-auto px-3 pt-4">
          {menuCategories.map((category, index) => (
            <div key={index} className="mb-8">
              <h2 className="px-3 mb-2 text-xs font-medium text-gray-500 dark:text-gray-400">
                {category.title}
              </h2>
              <ul className="space-y-1">
                {category.items.map((item) => (
                  <li key={item.id}>
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-start h-11",
                        activePage === item.id
                          ? "bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
                          : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-zinc-800"
                      )}
                      asChild
                    >
                      <Link to={`/admin/${item.id}`} onClick={() => setActivePage(item.id)}>
                        <span className="mr-3">{item.icon}</span>
                        <span>{item.label}</span>
                      </Link>
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </nav>

        <Separator />
        <div className="p-3 mt-2 flex items-center justify-between">
          <ModeToggle />
          <Button
            variant="ghost"
            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/30"
            onClick={handleLogout}
          >
            <LogOut className="h-5 w-5 mr-2" />
            <span>Abmelden</span>
          </Button>
        </div>
      </div>
    </aside>
  );
}
