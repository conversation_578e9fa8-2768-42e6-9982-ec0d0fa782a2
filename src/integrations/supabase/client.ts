// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

// Get Supabase credentials from environment variables
// These are set in Vercel's project settings
// Different values for preview (test DB) and production (production DB)
const SUPABASE_URL =
  import.meta.env.VITE_SUPABASE_URL ||
  // Fallback for local development
  (import.meta.env.DEV
    ? "https://ztqiadbfgjcxlnsdeycb.supabase.co"
    : "https://ztqiadbfgjcxlnsdeycb.supabase.co");

const SUPABASE_PUBLISHABLE_KEY =
  import.meta.env.VITE_SUPABASE_ANON_KEY ||
  // Fallback for local development
  (import.meta.env.DEV
    ? "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0cWlhZGJmZ2pjeGxuc2RleWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3NDk0MjAsImV4cCI6MjA2MDMyNTQyMH0.aEAEryuMfAdc6sK34Ka0uxkAm3RwTeTTIhZ1_jZGe-w"
    : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0cWlhZGJmZ2pjeGxuc2RleWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3NDk0MjAsImV4cCI6MjA2MDMyNTQyMH0.aEAEryuMfAdc6sK34Ka0uxkAm3RwTeTTIhZ1_jZGe-w");

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
