import React, { Suspense } from "react";
import { FeedbackForm } from "@/components/feedback/FeedbackForm";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import MatchHistory from "@/components/history/MatchHistory";
import LoadingSpinner from "@/components/LoadingSpinner";
import { ChevronLeft } from "lucide-react";
import { MobileLayout } from "@/components/layout/MobileLayout";

export default function Feedback() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col">
      <Header removeBoxShadow={true} />

      <MobileLayout>
        <main className="flex-1 px-4">
          <div className="space-y-6 max-w-screen-xl mx-auto pt-4">
            <div className="flex">
              <Button
                variant="ghost"
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 -ml-2"
                onClick={() => navigate(-1)}
              >
                <ChevronLeft className="mr-1 h-4 w-4" />
                Zurück
              </Button>
            </div>

            <Suspense
              fallback={
                <div className="w-full flex justify-center items-center py-12">
                  <div className="text-center">
                    <LoadingSpinner size="lg" />
                    <p className="mt-4 text-muted-foreground">Lade Feedback...</p>
                  </div>
                </div>
              }
            >
              <FeedbackForm />
            </Suspense>
          </div>
        </main>

        <Footer />
      </MobileLayout>
    </div>
  );
}
