import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate, Outlet } from "react-router-dom";
import LoadingSpinner from "@/components/LoadingSpinner";
import { useGameSessions } from "@/hooks/useGameSessions";
import { usePlayers } from "@/hooks/usePlayers";
import AdminPanel from "@/components/admin/AdminPanel";
import GameManagement from "@/components/admin/GameManagementPage";
import PlayerManagement from "@/components/admin/PlayerManagementPage";
import MVPManagement from "@/components/admin/MVPManagementPage";
import TeamPlayground from "@/components/admin/TeamPlayground";
import TournamentModePage from "@/components/admin/TournamentModePage";
import HistoryView from "@/components/history/MatchHistory";
import AdminSettings from "@/components/admin/AdminSettings";
import AdminStatistics from "@/components/admin/AdminStatistics";
import Footer from "@/components/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import AdminTopbar from "@/components/admin/AdminTopbar";
import AdminLogin from "@/components/admin/AdminLogin";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { FeedbackManagement } from "@/components/admin/FeedbackManagement";

interface AdminProps {
  page?: string;
}

export default function Admin({ page: propPage }: AdminProps) {
  // Get page from URL params if not provided as prop
  const { "*": urlPage } = useParams();
  const navigate = useNavigate();

  // Use the page from props or URL, defaulting to "dashboard"
  const initialPage = propPage || urlPage || "dashboard";
  const [activePage, setActivePage] = useState(initialPage);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const { currentSession, loading: sessionLoading } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });
  const { players, loading: playersLoading } = usePlayers(currentSession?.id);

  // Redirect to dashboard if no page is specified
  useEffect(() => {
    if (!urlPage && isAdminLoggedIn) {
      navigate("/admin/dashboard");
    }
  }, [urlPage, navigate, isAdminLoggedIn]);

  // Update active page when prop or URL changes
  useEffect(() => {
    if (propPage || urlPage) {
      setActivePage(propPage || urlPage || "dashboard");
    }
  }, [propPage, urlPage]);

  // Check admin login status
  useEffect(() => {
    const checkAdminAuth = () => {
      const adminLoggedIn = localStorage.getItem("isAdminLoggedIn") === "true";
      setIsAdminLoggedIn(adminLoggedIn);
      setIsCheckingAuth(false);
    };

    checkAdminAuth();
  }, []);

  // Handle successful login
  const handleLoginSuccess = () => {
    setIsAdminLoggedIn(true);
    navigate("/admin/dashboard");
  };

  // Set default sidebar state based on viewport width and detect mobile
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      setIsSidebarOpen(!mobile); // Open by default on desktop (1024px and above)
    };

    // Set initial state
    handleResize();

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(!isSidebarOpen);
  }, [isSidebarOpen]);

  // Auto-close sidebar when clicking content on mobile
  const handleContentClick = useCallback(() => {
    if (isMobile && isSidebarOpen) {
      setIsSidebarOpen(false);
    }
  }, [isSidebarOpen, isMobile]);

  // Update page title when active page changes and navigate to the new URL
  const setActivePageAndNavigate = useCallback(
    (page: string) => {
      setActivePage(page);
      navigate(`/admin/${page}`);
      if (isMobile) {
        setIsSidebarOpen(false);
      }
    },
    [isMobile, navigate]
  );

  // Get the title for the current page
  const getPageTitle = () => {
    switch (activePage) {
      case "dashboard":
        return "Dashboard";
      case "game-management":
        return "Spielplanung";
      case "player-management":
        return "Spielerverwaltung";
      case "mvp-management":
        return "MVP";
      case "team-playground":
        return "Team Playground";
      case "tournament-mode":
        return "Turniermodus";
      case "history":
        return "Historie";
      case "feedback":
        return "Feedback";
      case "statistics":
        return "Statistiken";
      case "settings":
        return "Einstellungen";
      default:
        return "Admin Panel";
    }
  };

  // Render the active page content when logged in
  const renderActivePage = () => {
    switch (activePage) {
      case "dashboard":
        return <AdminPanel />;
      case "game-management":
        return <GameManagement />;
      case "player-management":
        return <PlayerManagement />;
      case "mvp-management":
        return <MVPManagement />;
      case "team-playground":
        return <TeamPlayground />;
      case "tournament-mode":
        return <TournamentModePage />;
      case "history":
        return <HistoryView isAdminView={true} />;
      case "feedback":
        return <FeedbackManagement />;
      case "statistics":
        return <AdminStatistics />;
      case "settings":
        return <AdminSettings />;
      default:
        return <AdminPanel />;
    }
  };

  // Show loading spinner while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Show login screen without sidebar if not logged in as admin
  if (!isAdminLoggedIn) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        <main className="flex-1 px-4 flex items-center justify-center">
          <div className="w-full max-w-md">
            <Card className="shadow-lg">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-2">
                  <img src="/images/logo.png" alt="INTER NRW Logo" className="h-14 w-14" />
                </div>
                <CardTitle className="text-2xl">Admin Login</CardTitle>
                <CardDescription>
                  Melden Sie sich an, um auf den Admin-Bereich zuzugreifen
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminLogin onLoginSuccess={handleLoginSuccess} />
              </CardContent>
            </Card>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show full admin panel with sidebar when logged in
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <AdminSidebar
        activePage={activePage}
        setActivePage={setActivePageAndNavigate}
        isSidebarOpen={isSidebarOpen}
      />

      <AdminTopbar
        title={getPageTitle()}
        toggleSidebar={toggleSidebar}
        isSidebarOpen={isSidebarOpen}
      />

      <main
        className={`flex-1 transition-all duration-300 mt-16 px-4 relative z-10 ${
          isSidebarOpen ? "lg:pl-[17rem]" : "lg:pl-4"
        }`}
        onClick={handleContentClick}
      >
        <div className="max-w-screen-xl mx-auto pt-6">
          {sessionLoading || playersLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            renderActivePage()
          )}
        </div>
      </main>
      <Footer
        className={`transition-all duration-300 relative z-10 ${
          isSidebarOpen ? "lg:pl-[17rem]" : "lg:pl-4"
        }`}
      />

      {/* Overlay for mobile when sidebar is open */}
      {isMobile && isSidebarOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={handleContentClick} />
      )}
    </div>
  );
}
