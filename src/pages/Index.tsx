import { useState, useEffect, lazy, Suspense } from "react";
import { Link, useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Shield } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { IconCircleArrowRight } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { useGameSessions } from "@/context/GameSessionsProvider";
import { useActiveMVPVoting } from "@/hooks/useActiveMVPVoting";
import { MVPVotingBanner } from "@/components/mvp/MVPVotingBanner";
import { MobileLayout } from "@/components/layout/MobileLayout";

const GameInfo = lazy(() => import("@/components/GameInfo"));
const PlayerList = lazy(() => import("@/components/player-list/index"));
const TeamDisplay = lazy(() => import("@/components/TeamDisplay"));

const Index = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { currentSession } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });
  const { hasActiveVoting } = useActiveMVPVoting();
  const navigate = useNavigate();

  useEffect(() => {
    const checkAdminStatus = () => {
      try {
        const adminLoggedIn = localStorage.getItem("isAdminLoggedIn") === "true";
        setIsAdmin(adminLoggedIn);
      } catch (error) {
        console.error("Error accessing localStorage:", error);
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminStatus();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-6 max-w-3xl flex justify-center items-center">
          <LoadingSpinner size="lg" />
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header removeBoxShadow={true} />

      <MobileLayout>
        <main className="flex-1 px-4">
          <div className="max-w-screen-xl mx-auto pt-4">
            {hasActiveVoting && (
              <div className="mb-2">
                <MVPVotingBanner />
              </div>
            )}

            {isAdmin && (
              <div className="mb-4">
                <div className="bg-gray-100 dark:bg-zinc-800 p-3 rounded-lg flex items-center gap-2">
                  <Shield className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium dark:text-white">
                    Admin-Zugang aktiviert
                  </span>
                  <Link
                    to="/admin"
                    className="text-sm text-team-primary dark:text-team-primary/80 hover:underline font-medium"
                  >
                    <IconCircleArrowRight className="mr-1 text-gray-400 dark:text-gray-500" />
                  </Link>
                </div>
              </div>
            )}

            <div className="mb-8">
              <Suspense
                fallback={
                  <div className="py-8">
                    <LoadingSpinner />
                  </div>
                }
              >
                <GameInfo />
              </Suspense>
            </div>

            {currentSession?.isTeamGenerated && (
              <div className="mb-8">
                <Suspense
                  fallback={
                    <div className="py-8">
                      <LoadingSpinner />
                    </div>
                  }
                >
                  <TeamDisplay />
                </Suspense>
              </div>
            )}

            {currentSession && currentSession.status === "scheduled" && (
              <Suspense
                fallback={
                  <div className="py-8">
                    <LoadingSpinner />
                  </div>
                }
              >
                <PlayerList />
              </Suspense>
            )}
          </div>
        </main>

        <Footer />
      </MobileLayout>
    </div>
  );
};

export default Index;
