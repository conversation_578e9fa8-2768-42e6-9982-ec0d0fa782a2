import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, Outlet } from "react-router-dom";
import { useState, useEffect, lazy, Suspense } from "react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { ThemeProvider } from "@/components/theme-provider";
import { GameSessionsProvider } from "@/context/GameSessionsProvider";
import { PlayerProfileProvider } from "@/context/PlayerProfileContext";
import { PlayerProfileModal } from "@/components/player-profile/PlayerProfileModal";
import { MobileTabBar } from "@/components/navigation/MobileTabBar";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";

// Import Admin page directly instead of lazy loading it
import Admin from "./pages/Admin";

// Lazy load other page components
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));
const MatchRules = lazy(() => import("./pages/MatchRules"));
const Login = lazy(() => import("./pages/Login"));
const History = lazy(() => import("./pages/History"));
const Statistics = lazy(() => import("./pages/Statistics"));
const MVPVoting = lazy(() => import("./pages/MVPVoting"));
const MVPHistory = lazy(() => import("./pages/MVPHistory"));
const Impressum = lazy(() => import("./pages/Impressum"));
const Datenschutz = lazy(() => import("./pages/Datenschutz"));
const Feedback = lazy(() => import("./pages/Feedback"));

const queryClient = new QueryClient();

// Protected route component
const ProtectedRoute = ({
  children,
  requireAdmin = false,
}: {
  children: JSX.Element;
  requireAdmin?: boolean;
}) => {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState<boolean | null>(null);

  useEffect(() => {
    const checkLoginStatus = () => {
      const loggedIn = localStorage.getItem("isLoggedIn") === "true";
      const adminLoggedIn = localStorage.getItem("isAdminLoggedIn") === "true";
      setIsLoggedIn(loggedIn);
      setIsAdminLoggedIn(adminLoggedIn);
    };

    checkLoginStatus();
  }, []);

  if (isLoggedIn === null || (requireAdmin && isAdminLoggedIn === null)) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // If not logged in at all, redirect to login page
  if (!isLoggedIn) {
    return <Navigate to="/login" />;
  }

  // For admin routes, we don't redirect, we let the Admin component handle showing the login form
  // This fixes the issue with clicking on Admin Panel in the menu
  return children;
};

const App = () => (
  <ThemeProvider defaultTheme="system" storageKey="inter-nrw-theme">
    <QueryClientProvider client={queryClient}>
      <GameSessionsProvider>
        <PlayerProfileProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Suspense
                fallback={
                  <div className="min-h-screen flex justify-center items-center">
                    <LoadingSpinner size="lg" />
                  </div>
                }
              >
                <Routes>
                  <Route path="/login" element={<Login />} />
                  <Route
                    path="/"
                    element={
                      <ProtectedRoute>
                        <Index />
                      </ProtectedRoute>
                    }
                  />
                  {/* Admin routes with nested paths */}
                  <Route
                    path="/admin/*"
                    element={
                      <ProtectedRoute requireAdmin>
                        <Admin />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/match-rules"
                    element={
                      <ProtectedRoute>
                        <MatchRules />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/history"
                    element={
                      <ProtectedRoute>
                        <History />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/statistics"
                    element={
                      <ProtectedRoute>
                        <Statistics />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/mvp-voting"
                    element={
                      <ProtectedRoute>
                        <MVPVoting />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/mvp-history"
                    element={
                      <ProtectedRoute requireAdmin>
                        <MVPHistory />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/feedback"
                    element={
                      <ProtectedRoute>
                        <Feedback />
                      </ProtectedRoute>
                    }
                  />
                  {/* Legal pages - accessible without login */}
                  <Route path="/impressum" element={<Impressum />} />
                  <Route path="/datenschutz" element={<Datenschutz />} />
                  <Route
                    path="*"
                    element={
                      <ProtectedRoute>
                        <NotFound />
                      </ProtectedRoute>
                    }
                  />
                </Routes>
                <PlayerProfileModal />
                <MobileTabBar />
              </Suspense>
            </BrowserRouter>
            <Analytics />
            <SpeedInsights />
          </TooltipProvider>
        </PlayerProfileProvider>
      </GameSessionsProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;
